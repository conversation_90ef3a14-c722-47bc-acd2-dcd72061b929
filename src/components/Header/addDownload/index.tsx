import React, { useState } from 'react';
import { Modal, Tabs, Form, Input, Button, message } from 'antd';
import styles from './style.module.css';
import { exportSatelliteTrajectory } from './exportAPI';

const { TabPane } = Tabs;

interface DataExportProps {
  visible: boolean;
  onClose: () => void;
  onSubmit?: (result: any) => void;
}

interface SatelliteTrajectoryData {
  satelliteIds: string;
}

const DataExport: React.FC<DataExportProps> = ({ visible, onClose, onSubmit }) => {
  const [activeTab, setActiveTab] = useState<string>('satelliteTrajectory');
  const [satelliteTrajectoryForm] = Form.useForm<SatelliteTrajectoryData>();

  // 处理卫星轨迹导出提交
  const handleSatelliteTrajectorySubmit = async (values: SatelliteTrajectoryData) => {
    try {
      message.loading('正在导出卫星轨迹数据...', 0);
      
      const result = await exportSatelliteTrajectory(values.satelliteIds);
      
      message.destroy(); // 清除loading消息
      
      if (result) {
        message.success('卫星轨迹数据导出成功');
        if (onSubmit) {
          onSubmit({
            type: 'satelliteTrajectory',
            success: true,
            data: values
          });
        }
        handleClose();
      } else {
        message.error('卫星轨迹数据导出失败');
      }
    } catch (error: any) {
      message.destroy();
      message.error(`导出失败: ${error.message}`);
      console.error('卫星轨迹导出错误:', error);
    }
  };

  const handleClose = () => {
    satelliteTrajectoryForm.resetFields();
    onClose();
  };

  return (
    <Modal
      title="数据导出"
      visible={visible}
      onCancel={handleClose}
      footer={null}
      width={600}
      centered
      className={styles.dataExportModal}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="卫星轨迹" key="satelliteTrajectory">
          <Form
            form={satelliteTrajectoryForm}
            layout="vertical"
            onFinish={handleSatelliteTrajectorySubmit}
            className={styles.form}
          >
            <Form.Item
              name="satelliteIds"
              label="卫星ID"
              rules={[
                { required: true, message: '请输入卫星ID' },
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve();
                    
                    // 验证卫星ID格式：可以是单个ID或多个ID用逗号分隔
                    const ids = value.split(',').map((id: string) => id.trim());
                    const invalidIds = ids.filter((id: string) => !id || !/^[a-zA-Z0-9_-]+$/.test(id));
                    
                    if (invalidIds.length > 0) {
                      return Promise.reject(new Error('卫星ID格式不正确，只能包含字母、数字、下划线和连字符'));
                    }
                    
                    return Promise.resolve();
                  }
                }
              ]}
              extra="输入单个卫星ID或多个ID（用逗号分隔），例如：sat1 或 sat1,sat2,sat3"
            >
              <Input 
                placeholder="请输入卫星ID，多个ID用逗号分隔" 
                allowClear
              />
            </Form.Item>

            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                className={styles.submitButton}
              >
                导出卫星轨迹数据
              </Button>
            </Form.Item>
          </Form>
        </TabPane>
        
        {/* 预留其他导出类型的Tab */}
        {/* 
        <TabPane tab="通信数据" key="communicationData">
          <div style={{ padding: '20px', textAlign: 'center', color: '#888' }}>
            通信数据导出功能开发中...
          </div>
        </TabPane>
        
        <TabPane tab="网络拓扑" key="networkTopology">
          <div style={{ padding: '20px', textAlign: 'center', color: '#888' }}>
            网络拓扑导出功能开发中...
          </div>
        </TabPane>
        */}
      </Tabs>
    </Modal>
  );
};

export default DataExport;
