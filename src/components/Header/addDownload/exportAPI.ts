// 数据导出API - 调用 /export/satellite_trajectory 接口
import { notification } from 'antd';

// 卫星轨迹导出请求接口定义
export interface SatelliteTrajectoryExportRequest {
  satellite_ids: string;
}

// 卫星轨迹导出响应接口定义
export interface SatelliteTrajectoryExportResponse {
  success: boolean;
  message?: string;
  file_content?: string;
  filename?: string;
}

/**
 * 导出卫星轨迹数据
 * @param satelliteIds 卫星ID字符串，多个ID用逗号分隔
 * @returns Promise<boolean> 导出是否成功
 */
export const exportSatelliteTrajectory = async (satelliteIds: string): Promise<boolean> => {
  try {
    // 验证卫星ID不为空
    if (!satelliteIds.trim()) {
      console.error('卫星ID不能为空');
      notification.error({
        message: '导出失败',
        description: '卫星ID不能为空'
      });
      return false;
    }

    console.log(`导出卫星轨迹数据，卫星ID: ${satelliteIds}`);

    // 构建请求数据
    const requestData: SatelliteTrajectoryExportRequest = {
      satellite_ids: satelliteIds
    };

    // 调用后端API
    const response = await fetch('http://************:5001/api/export/satellite_trajectory', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    // 检查响应类型
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('text/csv')) {
      // CSV文件响应
      const blob = await response.blob();
      const filename = response.headers.get('content-disposition')?.match(/filename="(.+)"/)?.[1] || 'satellite_trajectory.csv';
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      console.log('卫星轨迹数据导出成功');
      notification.success({
        message: '导出成功',
        description: `卫星轨迹数据已导出为: ${filename}`
      });
      return true;
    } else {
      // JSON响应（错误情况）
      const result: SatelliteTrajectoryExportResponse = await response.json();
      
      if (result.success) {
        notification.success({
          message: '导出成功',
          description: result.message || '卫星轨迹数据导出成功'
        });
        return true;
      } else {
        notification.error({
          message: '导出失败',
          description: result.message || '服务器返回错误状态'
        });
        console.error('卫星轨迹数据导出失败:', result);
        return false;
      }
    }
  } catch (error: any) {
    console.error('导出卫星轨迹数据异常:', error);
    notification.error({
      message: '导出失败',
      description: `导出过程中发生错误: ${error.message}`
    });
    return false;
  }
};
