.dataExportModal {
  color: #fff !important;
}

/* 全局强制白色文字 */
.dataExportModal * {
  color: #fff !important;
}

/* 保持按钮和特殊元素的颜色 */
.dataExportModal .ant-btn-primary {
  color: #fff !important;
}

.dataExportModal .ant-tabs-tab-active,
.dataExportModal .ant-tabs-tab-active * {
  color: #1890ff !important;
}

.dataExportModal .ant-modal {
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

.dataExportModal .ant-modal-header {
  background-color: #1e1e1e;
  border-bottom: 1px solid #333;
}

.dataExportModal .ant-modal-title {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

.dataExportModal .ant-modal-close-x {
  color: #fff !important;
}

.dataExportModal .ant-modal-body {
  background-color: #1e1e1e;
  padding: 20px;
}

.dataExportModal .ant-tabs-tab {
  color: #fff !important;
}

.dataExportModal .ant-tabs-tab-active {
  color: #1890ff !important;
}

.dataExportModal .ant-tabs-tab:hover {
  color: #1890ff !important;
}

.dataExportModal .ant-tabs-ink-bar {
  background-color: #1890ff;
}

.dataExportModal .ant-tabs-content-holder {
  background-color: #1e1e1e;
}

.form {
  margin-top: 20px;
}

.form .ant-form-item-label > label {
  color: #fff !important;
  font-weight: 500;
}

.form .ant-form-item-required::before {
  color: #ff4d4f !important;
}

.form .ant-input {
  background-color: #2a2a2a !important;
  border: 1px solid #444 !important;
  color: #fff !important;
}

.form .ant-input:hover {
  border-color: #1890ff !important;
}

.form .ant-input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.form .ant-input::placeholder {
  color: #888 !important;
}

.form .ant-select {
  color: #fff !important;
}

.form .ant-select-selector {
  background-color: #2a2a2a !important;
  border: 1px solid #444 !important;
  color: #fff !important;
}

.form .ant-select-selector:hover {
  border-color: #1890ff !important;
}

.form .ant-select-focused .ant-select-selector {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.form .ant-select-selection-placeholder {
  color: #888 !important;
}

.submitButton {
  background-color: #1890ff;
  border-color: #1890ff;
  color: #fff;
  font-weight: 500;
  height: 40px;
  border-radius: 6px;
  width: 100%;
  margin-top: 16px;
}

.submitButton:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.submitButton:focus {
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 下拉框选项样式 */
.form .ant-select-dropdown {
  background-color: #2a2a2a;
  border: 1px solid #444;
}

.form .ant-select-item {
  color: #fff;
}

.form .ant-select-item:hover {
  background-color: #333;
}

.form .ant-select-item-option-selected {
  background-color: #1890ff;
  color: #fff;
}

/* 消息提示框样式 */
.ant-message .ant-message-notice {
  background-color: #2a2a2a;
  border: 1px solid #444;
  color: #fff;
}

.ant-message .ant-message-success .anticon {
  color: #52c41a;
}

.ant-message .ant-message-error .anticon {
  color: #ff4d4f;
}
